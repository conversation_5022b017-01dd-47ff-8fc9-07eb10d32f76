<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="drawerTitle"
    :width="470"
    :footer="null"
    @register="registerDrawer"
  >
    <div class="h-full flex flex-col -mt-4">
      <!-- 标签页导航 -->
      <div class="flex-none">
        <Tabs
          v-model:activeKey="activeTabKey"
          centered
          :tabBarGutter="100"
          class=""
          @change="handleTabChange"
        >
          <TabPane key="taskInfo" tab="任务信息" />
          <TabPane key="workflow" tab="流程环节" />
          <TabPane key="monitor" tab="监控画面" />
        </Tabs>
      </div>

      <!-- 标签页内容 -->
      <div class="flex-1 overflow-hidden">
        <!-- 任务信息标签页 -->
        <div v-show="activeTabKey === 'taskInfo'" class="h-full overflow-auto">
          <Description
            v-if="taskData && !showSkeleton"
            :column="1"
            :data="taskData"
            :schema="descSchema"
            :labelStyle="{
              width: '160px',
              minWidth: '150px',
            }"
            :bordered="false"
          />
          <Skeleton v-if="showSkeleton" active :paragraph="{ rows: 8 }" />
        </div>

        <!-- 流程环节标签页 -->
        <div v-show="activeTabKey === 'workflow'" class="h-full p-6 overflow-hidden">
          <div v-if="currentTaskId" class="h-full">
            <TaskStepList
              :key="currentTaskId"
              :params="{
                taskId: currentTaskId,
              }"
              class="h-full"
            />
          </div>
          <div v-else class="h-full flex items-center justify-center text-gray-400">
            <span>暂无流程环节数据</span>
          </div>
        </div>

        <!-- 监控画面标签页 -->
        <div v-show="activeTabKey === 'monitor'" class="h-full p-6 overflow-hidden">
          <div class="h-full flex flex-col">
            <!-- 监控画面切换按钮 -->
            <div class="flex-none mb-4">
              <Space>
                <Button
                  :type="monitorMode === 'live' ? 'primary' : 'default'"
                  @click="switchMonitorMode('live')"
                >
                  实时画面
                </Button>
                <Button
                  :type="monitorMode === 'record' ? 'primary' : 'default'"
                  @click="switchMonitorMode('record')"
                >
                  历史录像
                </Button>
              </Space>
            </div>

            <!-- 监控画面内容 -->
            <div class="flex-1 h-0 overflow-hidden">
              <!-- 实时监控画面 -->
              <div v-if="monitorMode === 'live'" class="h-full">
                <div v-if="liveStreams.length > 0" class="h-full flex flex-col">
                  <!-- 视频流切换标签 -->
                  <Tabs
                    v-model:activeKey="activeLiveStreamKey"
                    size="small"
                    class="flex-none"
                    @change="handleLiveStreamChange"
                  >
                    <TabPane
                      v-for="stream in liveStreams"
                      :key="stream.key"
                      :tab="stream.title"
                    />
                  </Tabs>

                  <!-- 当前选中的视频流播放器 -->
                  <div class="flex-1 h-0 overflow-hidden bg-black rounded">
                    <LivePlayer
                      v-if="currentLiveStream"
                      :key="currentLiveStream.key"
                      :path="currentLiveStream.path"
                      class="h-full"
                    />
                    <div v-else class="w-full h-full flex items-center justify-center text-white">
                      <span>请选择视频流</span>
                    </div>
                  </div>
                </div>
                <div v-else class="h-full flex items-center justify-center text-gray-400">
                  <span>暂无实时监控画面</span>
                </div>
              </div>

              <!-- 历史录像 -->
              <div v-else-if="monitorMode === 'record'" class="h-full">
                <RecordedBroadcast
                  v-if="currentTaskId"
                  :params="{
                    taskId: currentTaskId,
                    roleType: recordRoleType,
                  }"
                  class="h-full"
                />
                <div v-else class="h-full flex items-center justify-center text-gray-400">
                  <span>暂无历史录像数据</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { Description } from '@/components/Description';
  import { Tabs, TabPane, Skeleton, Space, Button } from 'ant-design-vue';
  import { controlTaskInfo, postControlTaskGetLiveVideoUrl } from '@/api/security/railwayWorkflowTask';
  import { descSchema } from './info';
  import TaskStepList from './TaskStepList/index.vue';
  import LivePlayer from '@/components/LivePlayer/index.vue';
  import RecordedBroadcast from '@/views/security/components/RecordedBroadcast/index.vue';

  defineOptions({ name: 'TaskDetailDrawer' });

  interface StreamConfig {
    key: string;
    title: string;
    path: string;
  }

  // 响应式数据
  const activeTabKey = ref('taskInfo');
  const currentTaskId = ref<string | number>();
  const taskData = ref<any>();
  const showSkeleton = ref(false);
  const drawerTitle = ref('详情');

  // 监控画面相关
  const monitorMode = ref<'live' | 'record'>('live');
  const liveStreams = ref<StreamConfig[]>([]);
  const activeLiveStreamKey = ref<string>('');
  const recordRoleType = ref('leader');

  // 计算属性
  const currentLiveStream = computed(() => {
    if (!activeLiveStreamKey.value || !liveStreams.value.length) {
      return null;
    }
    return liveStreams.value.find((stream) => stream.key === activeLiveStreamKey.value) || null;
  });

  // 抽屉注册
  const [registerDrawer, { closeDrawer }] = useDrawerInner(async (taskId: string | number) => {
    if (!taskId) {
      return closeDrawer();
    }

    showSkeleton.value = true;
    currentTaskId.value = taskId;

    try {
      // 获取任务详情
      const response = await controlTaskInfo(taskId);
      taskData.value = response;
      drawerTitle.value = '详情';

      // 获取实时监控流
      await loadLiveStreams(taskId);
    } catch (error) {
      console.error('加载任务详情失败:', error);
    } finally {
      showSkeleton.value = false;
    }
  });

  // 加载实时监控流
  async function loadLiveStreams(taskId: string | number) {
    try {
      const data = await postControlTaskGetLiveVideoUrl({ taskId });
      
      const roleMap = {
        leader: '工作领导人',
        contact: '驻站联络人',
        guardian: '地线监护人',
      };

      const streams = data
        .filter((item: any) => item.arGlassId)
        .map((item: any) => ({
          key: item.personChargeRoleType + item.personChargeId + item.arGlassId,
          title: item.personChargeName + '-' + roleMap[item.personChargeRoleType],
          path: `/${taskId}/${item.arGlassId}/${item.personChargeRoleType}/${item.personChargeId}`,
        }));

      liveStreams.value = streams;
      activeLiveStreamKey.value = streams.length > 0 ? streams[0].key : '';
    } catch (error) {
      console.error('加载实时监控流失败:', error);
      liveStreams.value = [];
    }
  }

  // 标签页切换
  function handleTabChange(key: string) {
    activeTabKey.value = key;
  }

  // 监控模式切换
  function switchMonitorMode(mode: 'live' | 'record') {
    monitorMode.value = mode;
  }

  // 实时流切换
  function handleLiveStreamChange(key: string) {
    activeLiveStreamKey.value = key;
  }
</script>

<style scoped>
.ant-tabs-content-holder {
  height: 100%;
}

.ant-tabs-tabpane {
  height: 100%;
}
</style>
